// prisma/schema.prisma

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Feed {
  id        String   @id @default(cuid())
  name      String
  url       String   @unique
  createdAt DateTime @default(now())
  items     Item[]
}

model Item {
  id          String    @id @default(cuid())
  feedId      String
  feed        Feed      @relation(fields: [feedId], references: [id])
  title       String
  url         String    @unique
  publishedAt DateTime?
  rawText     String?
  analysis    Analysis?
  createdAt   DateTime  @default(now())
}

model Analysis {
  id        String   @id @default(cuid())
  itemId    String   @unique
  item      Item     @relation(fields: [itemId], references: [id])
  summary   String?
  sentiment String?
  tags      String[]
  topics    String[]
  createdAt DateTime @default(now())
}

model IngestLog {
  id        String   @id @default(cuid())
  status    String
  note      String?
  createdAt DateTime @default(now())
}
